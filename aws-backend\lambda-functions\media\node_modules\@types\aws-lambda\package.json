{"name": "@types/aws-lambda", "version": "8.10.150", "description": "TypeScript definitions for aws-lambda", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/aws-lambda", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "darbio", "url": "https://github.com/darbio"}, {"name": "<PERSON>", "githubUsername": "skarum", "url": "https://github.com/skarum"}, {"name": "<PERSON><PERSON>", "githubUsername": "StefH", "url": "https://github.com/StefH"}, {"name": "<PERSON>", "githubUsername": "buggy", "url": "https://github.com/buggy"}, {"name": "wwwy3y3", "githubUsername": "wwwy3y3", "url": "https://github.com/wwwy3y3"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "OrthoDex", "url": "https://github.com/OrthoDex"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Michael<PERSON>ner"}, {"name": "<PERSON>", "githubUsername": "daniel-cottone", "url": "https://github.com/daniel-cottone"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "kostya-misura", "url": "https://github.com/kostya-misura"}, {"name": "<PERSON>", "githubUsername": "coderbyheart", "url": "https://github.com/coderbyheart"}, {"name": "<PERSON><PERSON>", "githubUsername": "palmithor", "url": "https://github.com/palmithor"}, {"name": "<PERSON><PERSON>", "githubUsername": "da<PERSON><PERSON><PERSON>", "url": "https://github.com/daniloraisi"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/simonbuchan"}, {"name": "<PERSON>", "githubUsername": "Haydabase", "url": "https://github.com/Haydabase"}, {"name": "<PERSON>", "githubUsername": "repl-chris", "url": "https://github.com/repl-chris"}, {"name": "<PERSON><PERSON>", "githubUsername": "aneilbaboo", "url": "https://github.com/aneilbaboo"}, {"name": "<PERSON>", "githubUsername": "jeznag", "url": "https://github.com/jeznag"}, {"name": "<PERSON>", "githubUsername": "louislarry", "url": "https://github.com/louislarry"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/dpapukchiev"}, {"name": "<PERSON>", "githubUsername": "ohookins", "url": "https://github.com/ohookins"}, {"name": "<PERSON>", "githubUsername": "trevor-leach", "url": "https://github.com/trevor-leach"}, {"name": "<PERSON>", "githubUsername": "jagregory", "url": "https://github.com/jagregory"}, {"name": "<PERSON>", "githubUsername": "dalen", "url": "https://github.com/dalen"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "loikg", "url": "https://github.com/loikg"}, {"name": "<PERSON>", "githubUsername": "skyzenr", "url": "https://github.com/skyzenr"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "redlick<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/redlickigrzegorz"}, {"name": "<PERSON>", "githubUsername": "juancarbonel", "url": "https://github.com/juancarbonel"}, {"name": "<PERSON>", "githubUsername": "pwm<PERSON><PERSON><PERSON>", "url": "https://github.com/pwmcintyre"}, {"name": "<PERSON>", "githubUsername": "alex-bolenok-centralreach", "url": "https://github.com/alex-bolenok-centralreach"}, {"name": "<PERSON>", "githubUsername": "marianzange", "url": "https://github.com/marianzange"}, {"name": "<PERSON>", "githubUsername": "apalumbo", "url": "https://github.com/apalumbo"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>chin<PERSON>"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/ivanmartos"}, {"name": "<PERSON>", "githubUsername": "zach-anthony", "url": "https://github.com/zach-anthony"}, {"name": "<PERSON>", "githubUsername": "savnik", "url": "https://github.com/savnik"}, {"name": "<PERSON><PERSON>", "githubUsername": "bboure", "url": "https://github.com/bboure"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/james<PERSON><PERSON>in"}, {"name": "<PERSON>", "githubUsername": "aphex", "url": "https://github.com/aphex"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/joe<PERSON><PERSON>patrick"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/lmanerich"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/LucianoTaranto"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/aws-lambda"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "8569013010f6344a33097495d7719b4f943191b6f8d984ab9988c029425d3f73", "typeScriptVersion": "5.1"}