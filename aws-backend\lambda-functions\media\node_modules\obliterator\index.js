/**
 * Obliterator Library Endpoint
 * =============================
 *
 * Exporting the library's functions.
 */
module.exports = {
  Iterator: require('./iterator.js'),
  chain: require('./chain.js'),
  combinations: require('./combinations.js'),
  consume: require('./consume.js'),
  filter: require('./filter.js'),
  forEach: require('./foreach.js'),
  map: require('./map.js'),
  match: require('./match.js'),
  permutations: require('./permutations.js'),
  powerSet: require('./power-set.js'),
  range: require('./range.js'),
  split: require('./split.js'),
  take: require('./take.js'),
  takeInto: require('./take-into.js')
};
