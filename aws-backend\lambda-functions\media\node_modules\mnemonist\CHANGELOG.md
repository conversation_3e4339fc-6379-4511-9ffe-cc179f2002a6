# Changelog

## 0.38.3

* Refactoring `VPTree` memory layout.
* Fixing `VPTree.nearestNeighbors` edge case.
* Various `VPTree` optimizations.

## 0.38.2

* Fixing `Heap.replace` & `Heap.pusphpop` types (@wholenews).

## 0.38.1

* Fixing `SparseQueueSet` deopt.

## 0.38.0

* Adding `TrieMap.update` (@wholenews).

## 0.37.0

* Adding `DefaultWeakMap` (@yoursunny).

## 0.36.1

* Improved typings for iteration methods (@yoursunny).

## 0.36.0

* Adding `SparseQueueSet`.

## 0.35.0

* Adding `SparseMap`.
* Enhancing `SparseSet` performance.

## 0.34.0

* Adding `set.overlap`.

## 0.33.1

* Fixing build by including missing `sort` folder.

## 0.33.0

* Adding `KDTree`.
* Adding `set.intersectionSize`.
* Adding `set.unionSize`.
* Adding `set.jaccard`.
* Adding `FixedReverseHeap.peek`.

## 0.32.0

* Adding `PassjoinIndex`.

## 0.31.3

* Fixing `Heap.nsmallest` & `Heap.nlargest` docs & typings.
* Fixing `Heap.nsmallest` & `Heap.nlargest` not using custom comparator function when `n = 1`.

## 0.31.2

* Fixing `BitSet` & `BitVector` iteration methods edge case.
* Fixing `BitSet` & `BitVector` `#.select` method.

## 0.31.1

* Fixing `BitSet` & `BitVector` `#.size` caching edge case.

## 0.31.0

* Adding `DefaultMap.peek`.
* Fixing some error messages.
* Fixing `BitSet` & `BitVector` `#.size` caching.

## 0.30.0

* Stricter TS definitions (`--noImplicitAny`, `--noImplicitReturns`) (@pbadenski).

## 0.29.0

* Adding `LRUCache.setpop` and `LRUMap.setpop` (@veggiesaurus).

## 0.28.0

* Adding `LRUCache.peek` and `LRUMap.peek` (@veggiesaurus).

## 0.27.2

* Fixing usage with TypeScript.

## 0.27.1

* Fixing `CircularBuffer` and `FixedDeque` types.

## 0.27.0

* Adding `FixedDeque`.
* Adding `CircularBuffer.unshift`.
* Changing `CircularBuffer` semantics to now overwrite values when wrapping around.

## 0.26.0

* Adding the `DefaultMap.autoIncrement` factory.
* Removing the `IncrementalMap`.
* Fixing `Vector` typings.
* Fixing `BitVector` typings.

## 0.25.1

* Fixing custom inspect methods for node >= 10.

## 0.25.0

* Adding `LRUCache`.
* Adding `LRUMap`.

## 0.24.0

* Adding `#.forEachMultiplicity` to `MultiSet`.
* Adding `#.forEachAssociation` to `MultiMap`.
* Adding `DefaultMap`.

## 0.23.0

* Adding `FixedReverseHeap`.
* Adding `Heap.nsmallest` & `Heap.nlargest`.
* Adding `MultiSet.isSubset` & `MultiSet.isSuperset`.
* Adding `#.top` to `MultiSet`.
* Adding missing `Heap` types.
* Renaming `FiniteStack` to `FixedStack`.

## 0.22.0

* Adding `FuzzyMultiMap.dimension`.
* Adding `#.consume` to `Heap`.
* Adding `#.replace` to `Heap`.
* Adding `#.pushpop` to `Heap`.
* Improving `BitSet` and `BitVector` `#.toJSON`.
* Improving `FiniteStack.from` & `CircularBuffer.from` performance when handling arrays.
* `Heap.from` is now linear time.
* Refactoring `Heap` inner logic.
* Fixing `CircularBuffer`'s `#.unshift` to `#.shift`.
* Fixing `SparseSet.delete` return consistency.

## 0.21.0

* Library is now fully typed.
* Adding `CircularBuffer`.
* Adding `#.toArray` to `Heap`.

## 0.20.0

* Adding `TrieMap`.
* Reworking the `Trie` considerably.

## 0.19.0

* Adding `StaticIntervalTree`.
* Adding `PointerVector`.
* Adding `Queue.of`.
* Adding `Stack.of`.
* Improving `Vector` & `BitVector` reallocation performance.
* Improving `InvertedIndex` performance.

## 0.18.O

* Adding `FiniteStack`.
* Adding `#.keys` to `MultiSet`.
* Adding `#.count` alias to `MultiSet`.
* Adding `#.count` alias to `MultiMap`.
* Adding `#.remove` to `MultiMap`.
* Adding `Vector.from`.
* Adding `#.values` to `Vector`.
* Adding `#.entries` to `Vector`.
* Fixing bug when feeding invalid values to a `MultiSet`.
* Fixing `.from` static methods not taking byte arrays into account.
* Fixing bugs related to `Stack.pop` edge cases.
* Optimizing `Stack` performance.

## 0.17.0

* Adding `HashedArrayTree`.
* Adding `BitVector`.
* Adding `#.frequency` to `MultiSet`.
* Adding `#.grow` to `DynamicArray`.
* Adding `#.reallocate` to `DynamicArray`.
* Adding `#.resize` to `DynamicArray`.
* Fixing several `MultiSet` issues.
* Renaming `DynamicArray` to `Vector`.
* Renaming the `DynamicArray.initialLength` option to `initialCapacity`.
* Renaming `DynamicArray.allocated` to `capacity`.
* Optimizing `MultiSet` performance.
* Optimizing `SparseSet` memory consumption.

## 0.16.0

* Adding `#.has` to `FuzzyMap`.
* Adding `#.has` to `FuzzyMultiMap`.
* Adding `#.multiplicity` to `MultiMap`.
* Renaming `RangeMap` to `IncrementalMap`.
* Renaming `Index` to `FuzzyMap`.
* Renaming `MultiIndex` to `FuzzyMultiMap`.
* Renaming `DynamicArray` `initialSize` option to `initialLength`.
* Improving `MultiMap.set` performance.
* Improving `BitSet.reset` performance.
* Improving `Set.isSubset` & `Set.isSuperset` performance.

## 0.15.0

* Adding `RangeMap`.
* Improving `MultiSet`.
* Out-of-bound `DynamicArray.set` will now correctly grow the array.
* Fixing `StaticDisjointSet.find` complexity.

## O.14.0

* Adding `DynamicArray`.
* Adding `SparseSet`.
* Adding `StaticDisjointSet`.
* Adding iterator methods to `BitSet`.
* Adding `#.rank` & `#.select` to `BitSet`.
* `BitSet` now relies on `Uint32Array` rather than `Uint8Array`.
* Improving `BitSet` performances.
* Using `obliterator` to handle iterators.

## 0.13.0

* Adding `BiMap`.
* Adding `BitSet`.
* Fixing universal iterator.

## 0.12.0

* Adding `InvertedIndex`.

## 0.11.0

* Adding bunch of set functions.

## 0.10.2

* Fixing error in `Trie.get`.
* Fixing error related to `Trie.size`.

## 0.10.1

* Fixing an error in `VPTree.neighbors`.

## 0.10.0

* Adding `Index`.
* Adding `MultiIndex`.
* Adding `MultiMap`.
* Adding `MultiSet`.
* Adding `SymSpell`.

## 0.9.0

* Adding `VPTree`.

## 0.8.0

* Adding `BKTree`.

## 0.7.0

* Adding `BloomFilter`.
* Adding static `#.from` method to all relevant structures.
* Adding iterators to all relevant structures.
* Removing the `MultiSet` until proper API is found.

## 0.6.0

* Adding `MultiSet`.

## 0.5.0

* Adding `SuffixArray` & `GeneralizedSuffixArray`.
* Better `Trie` sentinel.

## 0.4.0

* Adding `Queue`.
* Adding possibility to pass custom comparator to `Heap` & `FibonacciHeap`.

## 0.3.0

* Adding `FibonacciHeap`.
* Fixing bug related to `Heap`.

## 0.2.0

* Adding `Trie`.

## 0.1.0

* Adding `Heap`.

## 0.0.1

* Adding `LinkedList`.
* Adding `Stack`.
